@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}
/* Import Poppins font */
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

/* CSS Variables - Design System */
:root {
  /* Primary Brand Colors */
  --primary-blue: #4153ED;
  --primary-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);

  /* Success States */
  --success-primary: #10b981;
  --success-bg: #d1fae5;
  --success-text: #059669;

  /* Error States */
  --error-primary: #ef4444;
  --error-bg: #fee2e2;
  --error-text: #dc2626;

  /* Warning States */
  --warning-primary: #f59e0b;
  --warning-bg: #fef3c7;
  --warning-text: #d97706;

  /* Neutral Colors */
  --background-primary: linear-gradient(45deg, #f8f9fb 0%, #f8f9fb00 100%);
  --text-primary: #1E293B;
  --text-secondary: #64748B;
  --border-color: #e2e8f0;
  --white: #FFFFFF;

  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;

  /* Breakpoints */
  --mobile-max: 576px;
  --tablet-min: 577px;
  --tablet-max: 768px;
  --desktop-min: 769px;
  --large-desktop-min: 1024px;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  color: var(--text-primary);
  background: var(--background-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Responsive Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-semibold);
  line-height: 1.2;
  margin-bottom: 0.5rem;
}

/* Mobile Typography */
@media screen and (max-width: 576px) {
  h1 { font-size: 24px; }
  h2 { font-size: 20px; }
  h3 { font-size: 18px; }
  h4 { font-size: 16px; }
  body { font-size: 14px; }
}

/* Tablet Typography */
@media screen and (min-width: 577px) and (max-width: 768px) {
  h1 { font-size: 28px; }
  h2 { font-size: 24px; }
  h3 { font-size: 20px; }
  h4 { font-size: 18px; }
  body { font-size: 15px; }
}

/* Desktop Typography */
@media screen and (min-width: 769px) {
  h1 { font-size: 32px; }
  h2 { font-size: 28px; }
  h3 { font-size: 24px; }
  h4 { font-size: 20px; }
  body { font-size: 16px; }
}

/* Utility Classes */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 16px;
}

@media screen and (max-width: 576px) {
  .container {
    width: 98%;
    padding: 0 16px;
  }
}

@media screen and (min-width: 577px) and (max-width: 768px) {
  .container {
    width: 95%;
    padding: 0 24px;
  }
}

@media screen and (min-width: 769px) {
  .container {
    padding: 0 32px;
  }
}
