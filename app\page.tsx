import styles from './page.module.css';

export default function Home() {
  return (
    <div className={styles.main}>
      {/* Header */}
      <header className={styles.header}>
        <div className={styles.headerContent}>
          <div className={styles.userInfo}>
            <div className={styles.avatar}>
              <span>👤</span>
            </div>
            <div className={styles.greeting}>
              <h3>Hi, <PERSON></h3>
              <p>Welcome back</p>
            </div>
          </div>
          <div className={styles.notificationIcon}>
            <span>🔔</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className={styles.content}>
        <div className={styles.container}>

          {/* Hero Section */}
          <section className={styles.heroSection}>
            <h1>Explore new courses</h1>

            {/* Search Bar */}
            <div className={styles.searchContainer}>
              <div className={styles.searchBar}>
                <span className={styles.searchIcon}>🔍</span>
                <input
                  type="text"
                  placeholder="Search courses"
                  className={styles.searchInput}
                />
                <button className={styles.filterBtn}>⚙️</button>
              </div>
            </div>
          </section>

          {/* Categories */}
          <section className={styles.categoriesSection}>
            <div className={styles.categories}>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>🎓</div>
                <span>Business</span>
              </div>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>💻</div>
                <span>Tech</span>
              </div>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>🎨</div>
                <span>Design</span>
              </div>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>📊</div>
                <span>Marketing</span>
              </div>
            </div>
          </section>

          {/* Featured Course */}
          <section className={styles.featuredSection}>
            <div className={styles.featuredCard}>
              <div className={styles.courseImage}>
                <div className={styles.imagePlaceholder}>
                  <span>📚</span>
                </div>
                <button className={styles.favoriteBtn}>❤️</button>
              </div>
              <div className={styles.courseInfo}>
                <h3>Complete Web Development</h3>
                <p className={styles.instructor}>by John Smith</p>
                <div className={styles.courseStats}>
                  <span className={styles.rating}>⭐ 4.8</span>
                  <span className={styles.students}>2.5k students</span>
                </div>
                <button className={styles.seeRecipeBtn}>See course →</button>
              </div>
            </div>
          </section>

          {/* Popular Courses */}
          <section className={styles.popularSection}>
            <h2>Popular Courses</h2>
            <div className={styles.coursesGrid}>
              <div className={styles.popularCard}>
                <div className={styles.courseImage}>
                  <div className={styles.imagePlaceholder}>
                    <span>🚀</span>
                  </div>
                  <button className={styles.favoriteBtn}>❤️</button>
                </div>
                <div className={styles.courseInfo}>
                  <h3>React Masterclass</h3>
                  <p className={styles.instructor}>by Jane Doe</p>
                  <div className={styles.courseStats}>
                    <span className={styles.rating}>⭐ 4.9</span>
                    <span className={styles.students}>1.8k students</span>
                  </div>
                  <div className={styles.coursePrice}>$89</div>
                </div>
              </div>

              <div className={styles.popularCard}>
                <div className={styles.courseImage}>
                  <div className={styles.imagePlaceholder}>
                    <span>🎨</span>
                  </div>
                  <button className={styles.favoriteBtn}>🤍</button>
                </div>
                <div className={styles.courseInfo}>
                  <h3>UI/UX Design Fundamentals</h3>
                  <p className={styles.instructor}>by Mike Johnson</p>
                  <div className={styles.courseStats}>
                    <span className={styles.rating}>⭐ 4.7</span>
                    <span className={styles.students}>3.2k students</span>
                  </div>
                  <div className={styles.coursePrice}>$129</div>
                </div>
              </div>

              <div className={styles.popularCard}>
                <div className={styles.courseImage}>
                  <div className={styles.imagePlaceholder}>
                    <span>📱</span>
                  </div>
                  <button className={styles.favoriteBtn}>🤍</button>
                </div>
                <div className={styles.courseInfo}>
                  <h3>Mobile App Development</h3>
                  <p className={styles.instructor}>by Sarah Wilson</p>
                  <div className={styles.courseStats}>
                    <span className={styles.rating}>⭐ 4.8</span>
                    <span className={styles.students}>2.1k students</span>
                  </div>
                  <div className={styles.coursePrice}>$149</div>
                </div>
              </div>
            </div>
          </section>

          {/* My Courses Section */}
          <section className={styles.myCoursesSection}>
            <div className={styles.sectionHeader}>
              <h2>My Courses</h2>
              <button className={styles.viewAllBtn}>View All</button>
            </div>
            <div className={styles.myCoursesGrid}>
              <div className={styles.myCourseCard}>
                <div className={styles.courseImage}>
                  <div className={styles.imagePlaceholder}>
                    <span>💻</span>
                  </div>
                  <button className={styles.favoriteBtn}>❤️</button>
                </div>
                <div className={styles.courseInfo}>
                  <h3>JavaScript Essentials</h3>
                  <p className={styles.instructor}>by Alex Chen</p>
                  <div className={styles.progressBar}>
                    <div className={styles.progressFill} style={{width: '75%'}}></div>
                  </div>
                  <p className={styles.progressText}>75% Complete</p>
                </div>
              </div>
            </div>
          </section>

        </div>
      </main>

      {/* Bottom Navigation */}
      <nav className={styles.bottomNav}>
        <div className={styles.navItem}>
          <span className={styles.navIcon}>🏠</span>
          <span className={styles.navLabel}>Home</span>
        </div>
        <div className={styles.navItem}>
          <span className={styles.navIcon}>📖</span>
          <span className={styles.navLabel}>Courses</span>
        </div>
        <div className={styles.navItem}>
          <span className={styles.navIcon}>📊</span>
          <span className={styles.navLabel}>Progress</span>
        </div>
        <div className={styles.navItem}>
          <span className={styles.navIcon}>👤</span>
          <span className={styles.navLabel}>Profile</span>
        </div>
      </nav>
    </div>
  );
}
