import styles from './page.module.css';

export default function Home() {
  return (
    <div className={styles.main}>
      {/* Header */}
      <header className={styles.header}>
        <div className={styles.headerContent}>
          <div className={styles.userInfo}>
            <div className={styles.avatar}>
              <span>👤</span>
            </div>
            <div className={styles.greeting}>
              <h3>Hi, <PERSON></h3>
              <p>Welcome back</p>
            </div>
          </div>
          <div className={styles.notificationIcon}>
            <span>🔔</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className={styles.content}>
        <div className={styles.container}>

          {/* Hero Section */}
          <section className={styles.heroSection}>
            <h1>Course Completion Assistance</h1>
            <p className={styles.subtitle}>Professional help for your online courses</p>
          </section>

          {/* Carousel/Banner Section */}
          <section className={styles.bannerSection}>
            <div className={styles.bannerCard}>
              <div className={styles.bannerImage}>
                <span>🎓</span>
              </div>
              <div className={styles.bannerContent}>
                <h3>New Course Launch</h3>
                <p>Professional assistance for technical courses</p>
              </div>
            </div>
          </section>

          {/* Flash Sales Section */}
          <section className={styles.flashSalesSection}>
            <div className={styles.sectionHeader}>
              <h2>⚡ Flash Sales</h2>
              <span className={styles.countdown}>Ends in 2h 30m</span>
            </div>
            <div className={styles.flashSaleCard}>
              <div className={styles.flashBadge}>50% OFF</div>
              <div className={styles.courseImage}>
                <div className={styles.imagePlaceholder}>
                  <span>💻</span>
                </div>
              </div>
              <div className={styles.courseInfo}>
                <h3>Python Programming Assistance</h3>
                <div className={styles.priceInfo}>
                  <span className={styles.originalPrice}>$99</span>
                  <span className={styles.flashPrice}>$49</span>
                </div>
                <div className={styles.stockInfo}>
                  <span>65 sold</span>
                  <span>35 remaining</span>
                </div>
                <button className={styles.buyNowBtn}>Buy Now</button>
              </div>
            </div>
          </section>

          {/* Membership Packages */}
          <section className={styles.membershipSection}>
            <h2>Membership Packages</h2>
            <div className={styles.membershipGrid}>
              <div className={styles.membershipCard}>
                <div className={styles.membershipBadge}>VIP</div>
                <h3>Monthly VIP</h3>
                <div className={styles.membershipPrice}>$29.9</div>
                <ul className={styles.membershipBenefits}>
                  <li>✓ 20% discount on courses</li>
                  <li>✓ Priority support</li>
                  <li>✓ Free resources</li>
                </ul>
                <button className={styles.membershipBtn}>Subscribe</button>
              </div>

              <div className={styles.membershipCard}>
                <div className={styles.membershipBadge}>SUPER VIP</div>
                <h3>Annual VIP</h3>
                <div className={styles.membershipPrice}>$299</div>
                <ul className={styles.membershipBenefits}>
                  <li>✓ 30% discount on courses</li>
                  <li>✓ 24/7 priority support</li>
                  <li>✓ All resources included</li>
                  <li>✓ Priority updates</li>
                </ul>
                <button className={styles.membershipBtn}>Subscribe</button>
              </div>
            </div>
          </section>

          {/* Categories */}
          <section className={styles.categoriesSection}>
            <h2>Course Categories</h2>
            <div className={styles.categories}>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>💻</div>
                <span>Programming</span>
              </div>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>🎨</div>
                <span>Design</span>
              </div>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>📊</div>
                <span>Business</span>
              </div>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>🔬</div>
                <span>Science</span>
              </div>
            </div>
          </section>

          {/* Course Assistance Products */}
          <section className={styles.productsSection}>
            <h2>Popular Services</h2>
            <div className={styles.productsGrid}>
              <div className={styles.productCard}>
                <div className={styles.courseImage}>
                  <div className={styles.imagePlaceholder}>
                    <span>📚</span>
                  </div>
                  <div className={styles.productBadge}>HOT</div>
                </div>
                <div className={styles.courseInfo}>
                  <h3>Web Development Assistance</h3>
                  <p className={styles.platform}>Learning Platform A</p>
                  <div className={styles.priceInfo}>
                    <span className={styles.vipPrice}>$79</span>
                    <span className={styles.originalPrice}>$99</span>
                  </div>
                  <div className={styles.courseStats}>
                    <span className={styles.sales}>1,520 completed</span>
                  </div>
                  <button className={styles.orderBtn}>Order Now</button>
                </div>
              </div>

              <div className={styles.productCard}>
                <div className={styles.courseImage}>
                  <div className={styles.imagePlaceholder}>
                    <span>🚀</span>
                  </div>
                </div>
                <div className={styles.courseInfo}>
                  <h3>React Course Completion</h3>
                  <p className={styles.platform}>Learning Platform B</p>
                  <div className={styles.priceInfo}>
                    <span className={styles.vipPrice}>$89</span>
                    <span className={styles.originalPrice}>$129</span>
                  </div>
                  <div className={styles.courseStats}>
                    <span className={styles.sales}>890 completed</span>
                  </div>
                  <button className={styles.orderBtn}>Order Now</button>
                </div>
              </div>
            </div>
          </section>

          {/* Promotions Section */}
          <section className={styles.promotionsSection}>
            <h2>Special Offers</h2>
            <div className={styles.promotionsGrid}>
              <div className={styles.promotionCard}>
                <div className={styles.promotionIcon}>🎁</div>
                <div className={styles.promotionContent}>
                  <h3>Coupon Center</h3>
                  <p>Enter code to get discounts</p>
                  <div className={styles.couponInput}>
                    <input type="text" placeholder="Enter coupon code" />
                    <button>Apply</button>
                  </div>
                </div>
              </div>

              <div className={styles.promotionCard}>
                <div className={styles.promotionIcon}>💳</div>
                <div className={styles.promotionContent}>
                  <h3>Gift Cards</h3>
                  <p>Redeem your gift card balance</p>
                  <div className={styles.couponInput}>
                    <input type="text" placeholder="Gift card code" />
                    <button>Redeem</button>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Announcements Section */}
          <section className={styles.announcementsSection}>
            <h2>📢 Announcements</h2>
            <div className={styles.announcementsList}>
              <div className={styles.announcementItem}>
                <div className={styles.announcementIcon}>🔧</div>
                <div className={styles.announcementContent}>
                  <h4>System Maintenance Notice</h4>
                  <p>Scheduled maintenance tonight 22:00-24:00</p>
                  <span className={styles.announcementTime}>2 hours ago</span>
                </div>
              </div>

              <div className={styles.announcementItem}>
                <div className={styles.announcementIcon}>🎉</div>
                <div className={styles.announcementContent}>
                  <h4>New Platform Support</h4>
                  <p>We now support Smart Vocational Education MOOC</p>
                  <span className={styles.announcementTime}>1 day ago</span>
                </div>
              </div>
            </div>
          </section>

        </div>
      </main>

      {/* Bottom Navigation */}
      <nav className={styles.bottomNav}>
        <div className={`${styles.navItem} ${styles.active}`}>
          <span className={styles.navIcon}>🏠</span>
          <span className={styles.navLabel}>Home</span>
        </div>
        <div className={styles.navItem}>
          <span className={styles.navIcon}>🔍</span>
          <span className={styles.navLabel}>Explore</span>
        </div>
        <div className={styles.navItem}>
          <span className={styles.navIcon}>📋</span>
          <span className={styles.navLabel}>Orders</span>
        </div>
        <div className={styles.navItem}>
          <span className={styles.navIcon}>👤</span>
          <span className={styles.navLabel}>Profile</span>
        </div>
      </nav>
    </div>
  );
}
