import styles from './page.module.css';

export default function Home() {
  return (
    <div className={styles.main}>
      {/* Header */}
      <header className={styles.header}>
        <div className={styles.headerContent}>
          <div className={styles.userInfo}>
            <div className={styles.avatar}>
              <span>👤</span>
            </div>
            <div className={styles.greeting}>
              <h3>Hi, <PERSON></h3>
              <p>Welcome back</p>
            </div>
          </div>
          <div className={styles.notificationIcon}>
            <span>🔔</span>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className={styles.content}>
        <div className={styles.container}>

          {/* Hero Section */}
          <section className={styles.heroSection}>
            <h1>Explore course assistance</h1>

            {/* Search Bar */}
            <div className={styles.searchContainer}>
              <div className={styles.searchBar}>
                <span className={styles.searchIcon}>🔍</span>
                <input
                  type="text"
                  placeholder="Search assistance services"
                  className={styles.searchInput}
                />
                <button className={styles.filterBtn}>⚙️</button>
              </div>
            </div>
          </section>



          {/* Categories */}
          <section className={styles.categoriesSection}>
            <div className={styles.categories}>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>🍔</div>
                <span>Programming</span>
              </div>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>🥗</div>
                <span>Design</span>
              </div>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>🍕</div>
                <span>Business</span>
              </div>
              <div className={styles.categoryItem}>
                <div className={styles.categoryIcon}>🍝</div>
                <span>Science</span>
              </div>
            </div>
          </section>

          {/* Featured Service */}
          <section className={styles.featuredSection}>
            <div className={styles.featuredCard}>
              <div className={styles.courseImage}>
                <div className={styles.imagePlaceholder}>
                  <span>🥗</span>
                </div>
                <button className={styles.favoriteBtn}>❤️</button>
              </div>
              <div className={styles.courseInfo}>
                <h3>Complete Course Assistance</h3>
                <div className={styles.courseStats}>
                  <span className={styles.stat}>520</span>
                  <span className={styles.statLabel}>completed</span>
                  <span className={styles.stat}>98%</span>
                  <span className={styles.statLabel}>success</span>
                  <span className={styles.stat}>24</span>
                  <span className={styles.statLabel}>hours</span>
                  <span className={styles.stat}>$79</span>
                  <span className={styles.statLabel}>price</span>
                </div>
                <div className={styles.tags}>
                  <span className={styles.tag}>Programming</span>
                  <span className={styles.tag}>Fast</span>
                  <span className={styles.tag}>Reliable</span>
                </div>
                <button className={styles.seeRecipeBtn}>See service →</button>
              </div>
            </div>
          </section>

          {/* Popular Services */}
          <section className={styles.popularSection}>
            <div className={styles.popularCard}>
              <div className={styles.courseImage}>
                <div className={styles.imagePlaceholder}>
                  <span>🍗</span>
                </div>
                <button className={styles.favoriteBtn}>❤️</button>
              </div>
              <div className={styles.courseInfo}>
                <h3>Programming Course Help</h3>
                <div className={styles.courseStats}>
                  <span className={styles.stat}>420</span>
                  <span className={styles.statLabel}>completed</span>
                  <span className={styles.stat}>95%</span>
                  <span className={styles.statLabel}>success</span>
                </div>
              </div>
            </div>
          </section>



        </div>
      </main>

      {/* Bottom Navigation */}
      <nav className={styles.bottomNav}>
        <div className={`${styles.navItem} ${styles.active}`}>
          <span className={styles.navIcon}>🏠</span>
          <span className={styles.navLabel}>Home</span>
        </div>
        <div className={styles.navItem}>
          <span className={styles.navIcon}>🔍</span>
          <span className={styles.navLabel}>Explore</span>
        </div>
        <div className={styles.navItem}>
          <span className={styles.navIcon}>📋</span>
          <span className={styles.navLabel}>Orders</span>
        </div>
        <div className={styles.navItem}>
          <span className={styles.navIcon}>👤</span>
          <span className={styles.navLabel}>Profile</span>
        </div>
      </nav>
    </div>
  );
}
