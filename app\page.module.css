/* Main Layout */
.main {
  min-height: 100vh;
  background: var(--background-primary);
  display: flex;
  flex-direction: column;
  font-family: 'Poppins', sans-serif;/* Main Layout */
  .main {
    min-height: 100vh;
    background: var(--background-primary);
    display: flex;
    flex-direction: column;
    font-family: 'Poppins', sans-serif;
  }
  
  /* Header Styles */
  .header {
    padding: 16px 20px;
    background: var(--white);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: 100;
  }
  
  .headerContent {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
  }
  
  .userInfo {
    display: flex;
    align-items: center;
    gap: 12px;
  }
  
  .avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: var(--primary-gradient);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
  }
  
  .greeting h3 {
    margin: 0;
    font-size: 16px;
    font-weight: var(--font-semibold);
    color: var(--text-primary);
  }
  
  .greeting p {
    margin: 0;
    font-size: 12px;
    color: var(--text-secondary);
  }
  
  .notificationIcon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .notificationIcon:hover {
    background: var(--border-color);
  }
  
  /* Content Styles */
  .content {
    flex: 1;
    padding: 20px 0;
    margin-bottom: 80px; /* Space for bottom nav */
  }
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  /* Hero Section */
  .heroSection {
    margin-bottom: 32px;
  }
  
  .heroSection h1 {
    font-size: 28px;
    font-weight: var(--font-bold);
    color: var(--text-primary);
    margin-bottom: 20px;
  }
  
  .searchContainer {
    margin-bottom: 24px;
  }
  
  .searchBar {
    background: var(--white);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  }
  
  .searchIcon {
    font-size: 18px;
    color: var(--text-secondary);
  }
  
  .searchInput {
    flex: 1;
    border: none;
    outline: none;
    font-size: 16px;
    color: var(--text-primary);
    background: transparent;
  }
  
  .searchInput::placeholder {
    color: var(--text-secondary);
  }
  
  .filterBtn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 4px;
    border-radius: 8px;
    transition: background 0.3s ease;
  }
  
  .filterBtn:hover {
    background: var(--border-color);
  }
  
  /* Banner Section */
  .bannerSection {
    margin-bottom: 32px;
  }
  
  .bannerCard {
    background: var(--primary-gradient);
    border-radius: 20px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    color: var(--white);
  }
  
  .bannerImage {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
  }
  
  .bannerContent h3 {
    margin: 0 0 4px 0;
    font-size: 18px;
    font-weight: var(--font-semibold);
  }
  
  .bannerContent p {
    margin: 0;
    font-size: 14px;
    opacity: 0.9;
  }
  
  /* Flash Sales Section */
  .flashSalesSection {
    margin-bottom: 32px;
  }
  
  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
  }
  
  .sectionHeader h2 {
    margin: 0;
    font-size: 20px;
    font-weight: var(--font-semibold);
    color: var(--text-primary);
  }
  
  .countdown {
    background: var(--error-bg);
    color: var(--error-text);
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: var(--font-medium);
  }
  
  .flashSaleCard {
    background: var(--white);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    position: relative;
  }
  
  .flashBadge {
    position: absolute;
    top: 16px;
    left: 16px;
    background: var(--error-primary);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: var(--font-bold);
    z-index: 2;
  }
  
  .priceInfo {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 8px 0;
  }
  
  .originalPrice {
    text-decoration: line-through;
    color: var(--text-secondary);
    font-size: 14px;
  }
  
  .flashPrice {
    color: var(--error-primary);
    font-size: 18px;
    font-weight: var(--font-bold);
  }
  
  .stockInfo {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: var(--text-secondary);
    margin: 12px 0;
  }
  
  .buyNowBtn {
    background: var(--error-primary);
    color: var(--white);
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: var(--font-medium);
    cursor: pointer;
    width: 100%;
    transition: all 0.3s ease;
  }
  
  .buyNowBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
  }
  
  /* Membership Section */
  .membershipSection {
    margin-bottom: 32px;
  }
  
  .membershipSection h2 {
    font-size: 20px;
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: 16px;
  }
  
  .membershipGrid {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .membershipCard {
    background: var(--white);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    position: relative;
    transition: all 0.3s ease;
  }
  
  .membershipCard:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }
  
  .membershipBadge {
    position: absolute;
    top: -8px;
    right: 20px;
    background: var(--primary-gradient);
    color: var(--white);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: var(--font-bold);
  }
  
  .membershipCard h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    font-weight: var(--font-semibold);
  }
  
  .membershipPrice {
    font-size: 24px;
    font-weight: var(--font-bold);
    color: var(--primary-blue);
    margin-bottom: 16px;
  }
  
  .membershipBenefits {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
  }
  
  .membershipBenefits li {
    padding: 4px 0;
    font-size: 14px;
    color: var(--text-secondary);
  }
  
  .membershipBtn {
    background: var(--primary-gradient);
    color: var(--white);
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: var(--font-medium);
    cursor: pointer;
    width: 100%;
    transition: all 0.3s ease;
  }
  
  .membershipBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(65, 83, 237, 0.3);
  }
  
  /* Categories Section */
  .categoriesSection {
    margin-bottom: 32px;
  }
  
  .categories {
    display: flex;
    gap: 16px;
    overflow-x: auto;
    padding-bottom: 8px;
  }
  
  .categoryItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    min-width: 80px;
    cursor: pointer;
    transition: transform 0.3s ease;
  }
  
  .categoryItem:hover {
    transform: translateY(-2px);
  }
  
  .categoryIcon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
  }
  
  .categoryItem span {
    font-size: 12px;
    font-weight: var(--font-medium);
    color: var(--text-primary);
    text-align: center;
  }
  
  /* Featured Course Section */
  .featuredSection {
    margin-bottom: 32px;
  }
  
  .featuredCard {
    background: var(--white);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
  }
  
  .featuredCard:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }
  
  /* Popular Courses Section */
  .popularSection {
    margin-bottom: 32px;
  }
  
  .popularCard {
    background: var(--white);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
  }
  
  .popularCard:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }
  
  .courseImage {
    position: relative;
    margin-bottom: 16px;
  }
  
  .imagePlaceholder {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48px;
  }
  
  .favoriteBtn {
    position: absolute;
    top: 12px;
    right: 12px;
    background: var(--white);
    border: none;
    border-radius: 50%;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }
  
  .favoriteBtn:hover {
    transform: scale(1.1);
  }
  
  .courseInfo h3 {
    font-size: 20px;
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: 8px;
  }
  
  .instructor {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 12px;
  }
  
  .courseStats {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    align-items: center;
  }
  
  .stat {
    font-size: 16px;
    font-weight: var(--font-semibold);
    color: var(--text-primary);
  }
  
  .statLabel {
    font-size: 12px;
    color: var(--text-secondary);
    margin-right: 16px;
  }
  
  .tags {
    display: flex;
    gap: 8px;
    margin-bottom: 16px;
    flex-wrap: wrap;
  }
  
  .tag {
    background: var(--border-color);
    color: var(--text-secondary);
    padding: 4px 8px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: var(--font-medium);
  }
  
  .seeRecipeBtn {
    background: var(--primary-gradient);
    color: var(--white);
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .seeRecipeBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(65, 83, 237, 0.3);
  }
  
  /* Popular Courses Section */
  .popularSection {
    margin-bottom: 32px;
  }
  
  .popularSection h2 {
    font-size: 22px;
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: 20px;
  }
  
  .coursesGrid {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .popularCard {
    background: var(--white);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
  }
  
  .popularCard:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }
  
  .coursePrice {
    font-size: 18px;
    font-weight: var(--font-bold);
    color: var(--primary-blue);
    margin-top: 8px;
  }
  
  /* My Courses Section */
  .myCoursesSection {
    margin-bottom: 32px;
  }
  
  .sectionHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .sectionHeader h2 {
    font-size: 22px;
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin: 0;
  }
  
  .viewAllBtn {
    background: none;
    border: none;
    color: var(--primary-blue);
    font-size: 14px;
    font-weight: var(--font-medium);
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
  }
  
  .viewAllBtn:hover {
    background: var(--border-color);
  }
  
  .myCoursesGrid {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .myCourseCard {
    background: var(--white);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
  }
  
  .myCourseCard:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }
  
  .progressBar {
    width: 100%;
    height: 6px;
    background: var(--border-color);
    border-radius: 3px;
    margin: 12px 0 8px 0;
    overflow: hidden;
  }
  
  .progressFill {
    height: 100%;
    background: var(--success-primary);
    border-radius: 3px;
    transition: width 0.3s ease;
  }
  
  .progressText {
    font-size: 12px;
    color: var(--text-secondary);
    margin: 0;
  }
  
  /* Promotions Section */
  .promotionsSection {
    margin-bottom: 32px;
  }
  
  .promotionsSection h2 {
    font-size: 20px;
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: 16px;
  }
  
  .promotionsGrid {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .promotionCard {
    background: var(--white);
    border-radius: 20px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 16px;
  }
  
  .promotionIcon {
    width: 50px;
    height: 50px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    flex-shrink: 0;
  }
  
  .promotionContent {
    flex: 1;
  }
  
  .promotionContent h3 {
    margin: 0 0 4px 0;
    font-size: 16px;
    font-weight: var(--font-semibold);
  }
  
  .promotionContent p {
    margin: 0 0 12px 0;
    font-size: 12px;
    color: var(--text-secondary);
  }
  
  .couponInput {
    display: flex;
    gap: 8px;
  }
  
  .couponInput input {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    font-size: 12px;
    outline: none;
  }
  
  .couponInput button {
    background: var(--primary-blue);
    color: var(--white);
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: var(--font-medium);
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .couponInput button:hover {
    background: var(--primary-blue);
    opacity: 0.9;
  }
  
  /* Announcements Section */
  .announcementsSection {
    margin-bottom: 32px;
  }
  
  .announcementsSection h2 {
    font-size: 20px;
    font-weight: var(--font-semibold);
    color: var(--text-primary);
    margin-bottom: 16px;
  }
  
  .announcementsList {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
  
  .announcementItem {
    background: var(--white);
    border-radius: 16px;
    padding: 16px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    border: 1px solid var(--border-color);
    display: flex;
    align-items: flex-start;
    gap: 12px;
  }
  
  .announcementIcon {
    width: 36px;
    height: 36px;
    background: var(--warning-bg);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    flex-shrink: 0;
  }
  
  .announcementContent {
    flex: 1;
  }
  
  .announcementContent h4 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: var(--font-semibold);
    color: var(--text-primary);
  }
  
  .announcementContent p {
    margin: 0 0 8px 0;
    font-size: 12px;
    color: var(--text-secondary);
    line-height: 1.4;
  }
  
  .announcementTime {
    font-size: 10px;
    color: var(--text-secondary);
    opacity: 0.7;
  }
  
  /* Bottom Navigation */
  .bottomNav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: var(--white);
    border-top: 1px solid var(--border-color);
    padding: 12px 20px;
    display: flex;
    justify-content: space-around;
    align-items: center;
    z-index: 100;
  }
  
  .navItem {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 8px;
    border-radius: 12px;
  }
  
  .navItem:hover {
    background: var(--border-color);
  }
  
  .navIcon {
    font-size: 20px;
  }
  
  .navLabel {
    font-size: 10px;
    font-weight: var(--font-medium);
    color: var(--text-secondary);
  }
  
  .navItem.active {
    background: var(--primary-gradient);
    color: var(--white);
  }
  
  .navItem.active .navLabel {
    color: var(--white);
  }
  
  /* Mobile Responsive Styles */
  @media screen and (max-width: 576px) {
    .header {
      padding: 12px 16px;
    }
  
    .container {
      padding: 0 16px;
    }
  
    .heroSection h1 {
      font-size: 24px;
    }
  
    .searchBar {
      padding: 10px 14px;
    }
  
    .categories {
      gap: 12px;
    }
  
    .categoryItem {
      min-width: 70px;
    }
  
    .categoryIcon {
      width: 50px;
      height: 50px;
      font-size: 20px;
    }
  
    .featuredCard,
    .popularCard {
      padding: 16px;
      border-radius: 16px;
    }
  
    .imagePlaceholder {
      height: 160px;
      font-size: 36px;
    }
  
    .courseInfo h3 {
      font-size: 18px;
    }
  
    .bottomNav {
      padding: 8px 16px;
    }
  
    .navItem {
      padding: 6px;
    }
  
    .navIcon {
      font-size: 18px;
    }
  }
  
  /* Tablet Responsive Styles */
  @media screen and (min-width: 577px) and (max-width: 768px) {
    .container {
      padding: 0 24px;
    }
  
    .heroSection h1 {
      font-size: 26px;
    }
  
    .categories {
      gap: 20px;
    }
  
    .categoryIcon {
      width: 65px;
      height: 65px;
      font-size: 26px;
    }
  
    .featuredCard,
    .popularCard {
      padding: 24px;
    }
  
    .imagePlaceholder {
      height: 180px;
      font-size: 42px;
    }
  }
  
  /* Desktop Responsive Styles */
  @media screen and (min-width: 769px) {
    .header {
      padding: 20px 32px;
    }
  
    .container {
      padding: 0 32px;
    }
  
    .heroSection h1 {
      font-size: 32px;
    }
  
    .categories {
      gap: 24px;
      justify-content: flex-start;
    }
  
    .categoryItem {
      min-width: 90px;
    }
  
    .categoryIcon {
      width: 70px;
      height: 70px;
      font-size: 28px;
    }
  
    .featuredCard,
    .popularCard,
    .myCourseCard {
      padding: 28px;
    }
  
    .imagePlaceholder {
      height: 220px;
      font-size: 52px;
    }
  
    .courseInfo h3 {
      font-size: 22px;
    }
  
    /* Grid layouts for desktop */
    .membershipGrid {
      display: flex;
      flex-direction: row;
      gap: 24px;
    }
  
    .membershipCard {
      flex: 1;
      max-width: calc(50% - 12px);
    }
  
    .productsGrid {
      display: flex;
      flex-direction: row;
      gap: 24px;
      flex-wrap: wrap;
    }
  
    .productCard {
      flex: 1;
      min-width: 300px;
      max-width: calc(50% - 12px);
    }
  
    .promotionsGrid {
      display: flex;
      flex-direction: row;
      gap: 24px;
    }
  
    .promotionCard {
      flex: 1;
    }
  
    /* Hide bottom nav on desktop, show sidebar instead */
    .bottomNav {
      display: none;
    }
  
    .content {
      margin-bottom: 0;
    }
  }
  
  /* Large Desktop Responsive Styles */
  @media screen and (min-width: 1024px) {
    .productsGrid {
      gap: 32px;
    }
  
    .productCard {
      max-width: calc(33.333% - 22px);
    }
  
    .promotionsGrid {
      gap: 32px;
    }
  }
  
  /* Touch targets for mobile */
  @media screen and (max-width: 576px) {
    .favoriteBtn,
    .filterBtn,
    .navItem {
      min-height: 44px;
      min-width: 44px;
    }
  
    .seeRecipeBtn {
      padding: 14px 20px;
      font-size: 16px;
    }
  
    .viewAllBtn {
      padding: 12px 16px;
      font-size: 16px;
    }
  
    /* Ensure proper spacing on mobile */
    .popularSection h2,
    .sectionHeader h2 {
      font-size: 20px;
    }
  
    .coursePrice {
      font-size: 16px;
    }
  
    /* Improve touch scrolling for categories */
    .categories {
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none;
      -ms-overflow-style: none;
    }
  
    .categories::-webkit-scrollbar {
      display: none;
    }
  }
  
  /* Tablet specific improvements */
  @media screen and (min-width: 577px) and (max-width: 768px) {
    .membershipGrid {
      display: flex;
      flex-direction: row;
      gap: 20px;
    }
  
    .membershipCard {
      flex: 1;
    }
  
    .productsGrid {
      display: flex;
      flex-direction: row;
      gap: 20px;
      flex-wrap: wrap;
    }
  
    .productCard {
      flex: 1;
      min-width: calc(50% - 10px);
    }
  
    .promotionsGrid {
      display: flex;
      flex-direction: row;
      gap: 20px;
    }
  
    .promotionCard {
      flex: 1;
    }
  }
  
  /* Hover effects only on devices that support hover */
  @media (hover: hover) and (pointer: fine) {
    .categoryItem:hover {
      transform: translateY(-2px);
    }
  
    .featuredCard:hover,
    .popularCard:hover {
      transform: translateY(-4px);
      box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
    }
  
    .favoriteBtn:hover {
      transform: scale(1.1);
    }
  
    .seeRecipeBtn:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(65, 83, 237, 0.3);
    }
  }
  
}

/* Header Styles */
.header {
  padding: 16px 20px;
  background: var(--white);
  border-bottom: 1px solid var(--border-color);
  position: sticky;
  top: 0;
  z-index: 100;
}

.headerContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-gradient);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}

.greeting h3 {
  margin: 0;
  font-size: 16px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.greeting p {
  margin: 0;
  font-size: 12px;
  color: var(--text-secondary);
}

.notificationIcon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f8f9fa;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.notificationIcon:hover {
  background: var(--border-color);
}

/* Content Styles */
.content {
  flex: 1;
  padding: 20px 0;
  margin-bottom: 80px; /* Space for bottom nav */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Hero Section */
.heroSection {
  margin-bottom: 32px;
}

.heroSection h1 {
  font-size: 28px;
  font-weight: var(--font-bold);
  color: var(--text-primary);
  margin-bottom: 20px;
}

.searchContainer {
  margin-bottom: 24px;
}

.searchBar {
  background: var(--white);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.searchIcon {
  font-size: 18px;
  color: var(--text-secondary);
}

.searchInput {
  flex: 1;
  border: none;
  outline: none;
  font-size: 16px;
  color: var(--text-primary);
  background: transparent;
}

.searchInput::placeholder {
  color: var(--text-secondary);
}

.filterBtn {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 8px;
  transition: background 0.3s ease;
}

.filterBtn:hover {
  background: var(--border-color);
}

/* Banner Section */
.bannerSection {
  margin-bottom: 32px;
}

.bannerCard {
  background: var(--primary-gradient);
  border-radius: 20px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 16px;
  color: var(--white);
}

.bannerImage {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.bannerContent h3 {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: var(--font-semibold);
}

.bannerContent p {
  margin: 0;
  font-size: 14px;
  opacity: 0.9;
}

/* Flash Sales Section */
.flashSalesSection {
  margin-bottom: 32px;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.sectionHeader h2 {
  margin: 0;
  font-size: 20px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.countdown {
  background: var(--error-bg);
  color: var(--error-text);
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: var(--font-medium);
}

.flashSaleCard {
  background: var(--white);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  position: relative;
}

.flashBadge {
  position: absolute;
  top: 16px;
  left: 16px;
  background: var(--error-primary);
  color: var(--white);
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: var(--font-bold);
  z-index: 2;
}

.priceInfo {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 8px 0;
}

.originalPrice {
  text-decoration: line-through;
  color: var(--text-secondary);
  font-size: 14px;
}

.flashPrice {
  color: var(--error-primary);
  font-size: 18px;
  font-weight: var(--font-bold);
}

.stockInfo {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-secondary);
  margin: 12px 0;
}

.buyNowBtn {
  background: var(--error-primary);
  color: var(--white);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: var(--font-medium);
  cursor: pointer;
  width: 100%;
  transition: all 0.3s ease;
}

.buyNowBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

/* Membership Section */
.membershipSection {
  margin-bottom: 32px;
}

.membershipSection h2 {
  font-size: 20px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: 16px;
}

.membershipGrid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.membershipCard {
  background: var(--white);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  position: relative;
  transition: all 0.3s ease;
}

.membershipCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.membershipBadge {
  position: absolute;
  top: -8px;
  right: 20px;
  background: var(--primary-gradient);
  color: var(--white);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: var(--font-bold);
}

.membershipCard h3 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: var(--font-semibold);
}

.membershipPrice {
  font-size: 24px;
  font-weight: var(--font-bold);
  color: var(--primary-blue);
  margin-bottom: 16px;
}

.membershipBenefits {
  list-style: none;
  padding: 0;
  margin: 0 0 20px 0;
}

.membershipBenefits li {
  padding: 4px 0;
  font-size: 14px;
  color: var(--text-secondary);
}

.membershipBtn {
  background: var(--primary-gradient);
  color: var(--white);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: var(--font-medium);
  cursor: pointer;
  width: 100%;
  transition: all 0.3s ease;
}

.membershipBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(65, 83, 237, 0.3);
}

/* Categories Section */
.categoriesSection {
  margin-bottom: 32px;
}

.categories {
  display: flex;
  gap: 16px;
  overflow-x: auto;
  padding-bottom: 8px;
}

.categoryItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 80px;
  cursor: pointer;
  transition: transform 0.3s ease;
}

.categoryItem:hover {
  transform: translateY(-2px);
}

.categoryIcon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
}

.categoryItem span {
  font-size: 12px;
  font-weight: var(--font-medium);
  color: var(--text-primary);
  text-align: center;
}

/* Featured Course Section */
.featuredSection {
  margin-bottom: 32px;
}

.featuredCard {
  background: var(--white);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.featuredCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

/* Popular Courses Section */
.popularSection {
  margin-bottom: 32px;
}

.popularCard {
  background: var(--white);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.popularCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.courseImage {
  position: relative;
  margin-bottom: 16px;
}

.imagePlaceholder {
  width: 100%;
  height: 200px;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48px;
}

.favoriteBtn {
  position: absolute;
  top: 12px;
  right: 12px;
  background: var(--white);
  border: none;
  border-radius: 50%;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  cursor: pointer;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.favoriteBtn:hover {
  transform: scale(1.1);
}

.courseInfo h3 {
  font-size: 20px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: 8px;
}

.instructor {
  font-size: 14px;
  color: var(--text-secondary);
  margin-bottom: 12px;
}

.courseStats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
  align-items: center;
}

.stat {
  font-size: 16px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.statLabel {
  font-size: 12px;
  color: var(--text-secondary);
  margin-right: 16px;
}

.tags {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.tag {
  background: var(--border-color);
  color: var(--text-secondary);
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: var(--font-medium);
}

.seeRecipeBtn {
  background: var(--primary-gradient);
  color: var(--white);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all 0.3s ease;
}

.seeRecipeBtn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(65, 83, 237, 0.3);
}

/* Popular Courses Section */
.popularSection {
  margin-bottom: 32px;
}

.popularSection h2 {
  font-size: 22px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: 20px;
}

.coursesGrid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.popularCard {
  background: var(--white);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.popularCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.coursePrice {
  font-size: 18px;
  font-weight: var(--font-bold);
  color: var(--primary-blue);
  margin-top: 8px;
}

/* My Courses Section */
.myCoursesSection {
  margin-bottom: 32px;
}

.sectionHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.sectionHeader h2 {
  font-size: 22px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin: 0;
}

.viewAllBtn {
  background: none;
  border: none;
  color: var(--primary-blue);
  font-size: 14px;
  font-weight: var(--font-medium);
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.viewAllBtn:hover {
  background: var(--border-color);
}

.myCoursesGrid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.myCourseCard {
  background: var(--white);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  transition: all 0.3s ease;
}

.myCourseCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.progressBar {
  width: 100%;
  height: 6px;
  background: var(--border-color);
  border-radius: 3px;
  margin: 12px 0 8px 0;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: var(--success-primary);
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progressText {
  font-size: 12px;
  color: var(--text-secondary);
  margin: 0;
}

/* Promotions Section */
.promotionsSection {
  margin-bottom: 32px;
}

.promotionsSection h2 {
  font-size: 20px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: 16px;
}

.promotionsGrid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.promotionCard {
  background: var(--white);
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  gap: 16px;
}

.promotionIcon {
  width: 50px;
  height: 50px;
  background: var(--primary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  flex-shrink: 0;
}

.promotionContent {
  flex: 1;
}

.promotionContent h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: var(--font-semibold);
}

.promotionContent p {
  margin: 0 0 12px 0;
  font-size: 12px;
  color: var(--text-secondary);
}

.couponInput {
  display: flex;
  gap: 8px;
}

.couponInput input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 12px;
  outline: none;
}

.couponInput button {
  background: var(--primary-blue);
  color: var(--white);
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 12px;
  font-weight: var(--font-medium);
  cursor: pointer;
  transition: all 0.3s ease;
}

.couponInput button:hover {
  background: var(--primary-blue);
  opacity: 0.9;
}

/* Announcements Section */
.announcementsSection {
  margin-bottom: 32px;
}

.announcementsSection h2 {
  font-size: 20px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
  margin-bottom: 16px;
}

.announcementsList {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.announcementItem {
  background: var(--white);
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-color);
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.announcementIcon {
  width: 36px;
  height: 36px;
  background: var(--warning-bg);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.announcementContent {
  flex: 1;
}

.announcementContent h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: var(--font-semibold);
  color: var(--text-primary);
}

.announcementContent p {
  margin: 0 0 8px 0;
  font-size: 12px;
  color: var(--text-secondary);
  line-height: 1.4;
}

.announcementTime {
  font-size: 10px;
  color: var(--text-secondary);
  opacity: 0.7;
}

/* Bottom Navigation */
.bottomNav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--white);
  border-top: 1px solid var(--border-color);
  padding: 12px 20px;
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 100;
}

.navItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 8px;
  border-radius: 12px;
}

.navItem:hover {
  background: var(--border-color);
}

.navIcon {
  font-size: 20px;
}

.navLabel {
  font-size: 10px;
  font-weight: var(--font-medium);
  color: var(--text-secondary);
}

.navItem.active {
  background: var(--primary-gradient);
  color: var(--white);
}

.navItem.active .navLabel {
  color: var(--white);
}

/* Mobile Responsive Styles */
@media screen and (max-width: 576px) {
  .header {
    padding: 12px 16px;
  }

  .container {
    padding: 0 16px;
  }

  .heroSection h1 {
    font-size: 24px;
  }

  .searchBar {
    padding: 10px 14px;
  }

  .categories {
    gap: 12px;
  }

  .categoryItem {
    min-width: 70px;
  }

  .categoryIcon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .featuredCard,
  .popularCard {
    padding: 16px;
    border-radius: 16px;
  }

  .imagePlaceholder {
    height: 160px;
    font-size: 36px;
  }

  .courseInfo h3 {
    font-size: 18px;
  }

  .bottomNav {
    padding: 8px 16px;
  }

  .navItem {
    padding: 6px;
  }

  .navIcon {
    font-size: 18px;
  }
}

/* Tablet Responsive Styles */
@media screen and (min-width: 577px) and (max-width: 768px) {
  .container {
    padding: 0 24px;
  }

  .heroSection h1 {
    font-size: 26px;
  }

  .categories {
    gap: 20px;
  }

  .categoryIcon {
    width: 65px;
    height: 65px;
    font-size: 26px;
  }

  .featuredCard,
  .popularCard {
    padding: 24px;
  }

  .imagePlaceholder {
    height: 180px;
    font-size: 42px;
  }
}

/* Desktop Responsive Styles */
@media screen and (min-width: 769px) {
  .header {
    padding: 20px 32px;
  }

  .container {
    padding: 0 32px;
  }

  .heroSection h1 {
    font-size: 32px;
  }

  .categories {
    gap: 24px;
    justify-content: flex-start;
  }

  .categoryItem {
    min-width: 90px;
  }

  .categoryIcon {
    width: 70px;
    height: 70px;
    font-size: 28px;
  }

  .featuredCard,
  .popularCard,
  .myCourseCard {
    padding: 28px;
  }

  .imagePlaceholder {
    height: 220px;
    font-size: 52px;
  }

  .courseInfo h3 {
    font-size: 22px;
  }

  /* Grid layouts for desktop */
  .membershipGrid {
    display: flex;
    flex-direction: row;
    gap: 24px;
  }

  .membershipCard {
    flex: 1;
    max-width: calc(50% - 12px);
  }

  .productsGrid {
    display: flex;
    flex-direction: row;
    gap: 24px;
    flex-wrap: wrap;
  }

  .productCard {
    flex: 1;
    min-width: 300px;
    max-width: calc(50% - 12px);
  }

  .promotionsGrid {
    display: flex;
    flex-direction: row;
    gap: 24px;
  }

  .promotionCard {
    flex: 1;
  }

  /* Hide bottom nav on desktop, show sidebar instead */
  .bottomNav {
    display: none;
  }

  .content {
    margin-bottom: 0;
  }
}

/* Large Desktop Responsive Styles */
@media screen and (min-width: 1024px) {
  .productsGrid {
    gap: 32px;
  }

  .productCard {
    max-width: calc(33.333% - 22px);
  }

  .promotionsGrid {
    gap: 32px;
  }
}

/* Touch targets for mobile */
@media screen and (max-width: 576px) {
  .favoriteBtn,
  .filterBtn,
  .navItem {
    min-height: 44px;
    min-width: 44px;
  }

  .seeRecipeBtn {
    padding: 14px 20px;
    font-size: 16px;
  }

  .viewAllBtn {
    padding: 12px 16px;
    font-size: 16px;
  }

  /* Ensure proper spacing on mobile */
  .popularSection h2,
  .sectionHeader h2 {
    font-size: 20px;
  }

  .coursePrice {
    font-size: 16px;
  }

  /* Improve touch scrolling for categories */
  .categories {
    -webkit-overflow-scrolling: touch;
    scrollbar-width: none;
    -ms-overflow-style: none;
  }

  .categories::-webkit-scrollbar {
    display: none;
  }
}

/* Tablet specific improvements */
@media screen and (min-width: 577px) and (max-width: 768px) {
  .membershipGrid {
    display: flex;
    flex-direction: row;
    gap: 20px;
  }

  .membershipCard {
    flex: 1;
  }

  .productsGrid {
    display: flex;
    flex-direction: row;
    gap: 20px;
    flex-wrap: wrap;
  }

  .productCard {
    flex: 1;
    min-width: calc(50% - 10px);
  }

  .promotionsGrid {
    display: flex;
    flex-direction: row;
    gap: 20px;
  }

  .promotionCard {
    flex: 1;
  }
}

/* Hover effects only on devices that support hover */
@media (hover: hover) and (pointer: fine) {
  .categoryItem:hover {
    transform: translateY(-2px);
  }

  .featuredCard:hover,
  .popularCard:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }

  .favoriteBtn:hover {
    transform: scale(1.1);
  }

  .seeRecipeBtn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(65, 83, 237, 0.3);
  }
}
